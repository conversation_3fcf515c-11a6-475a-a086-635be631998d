{% extends 'TeacherDashboard/base.html' %}
{% load static %}
{% load humanize %}

{% block title-icon %}fas fa-chalkboard-teacher{% endblock title-icon %}
{% block title %}{{ staff.fullname|default:"My Profile" }}{% endblock title %}
{% block subtitle %}Teaching Staff Profile{% endblock subtitle %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Dashboard
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-user-cog"></i> My Profile
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block content-header %}
<!-- Modern Industry-Ready Staff Profile Header -->
<div class="staff-profile-container mb-4">
  <!-- Top Banner with Background Image -->
  <div class="profile-banner position-relative mb-4">
    <div class="banner-overlay"></div>
    <div class="container-fluid px-4 py-5">
      <div class="row align-items-end">
        <div class="col-md-7">
          <div class="d-flex align-items-center">
            <!-- Profile Image with Status Indicator -->
            <div class="profile-image-container position-relative me-4">
              {% if staff.passport %}
              <img src="{{ staff.passport.url }}" class="profile-image" alt="{{ staff.fullname }}">
              {% else %}
              <img src="{% static 'dist/img/teacher-dp.jpg' %}" class="profile-image" alt="{{ staff.fullname }}">
              {% endif %}

              <!-- Status Indicator -->
              <div class="status-indicator-modern {% if staff.current_status == 'active' %}active{% else %}inactive{% endif %}">
                <i class="fas {% if staff.current_status == 'active' %}fa-check{% else %}fa-times{% endif %}"></i>
              </div>
            </div>

            <!-- Staff Information -->
            <div class="staff-info text-white">
              <h2 class="staff-name mb-1">{{ staff.fullname|default:"Teacher Name" }}</h2>
              <div class="staff-designation mb-2">
                <span class="designation-badge">Teaching Staff</span>
                <span class="id-badge"><i class="fas fa-id-card me-1"></i>{{ staff.registration_number|default:"N/A" }}</span>
              </div>
              <div class="staff-specialization">
                <i class="fas fa-book-reader me-1"></i> {{ staff.Subject_specification|default:"Subject not specified" }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Staff Profile Stats -->
  <div class="staff-stats-container mb-4">
    <div class="container-fluid px-4">
      <div class="row">
        <div class="col-md-12">
          <div class="stats-card-wrapper">
            <div class="stats-card">
              <div class="stats-icon bg-primary-light">
                <i class="fas fa-calendar-check text-primary"></i>
              </div>
              <div class="stats-info">
                <h3 class="stats-value">{{ staff.date_of_registration|date:"d M Y"|default:"N/A" }}</h3>
                <p class="stats-label">Joined Date</p>
              </div>
            </div>

            <div class="stats-card">
              <div class="stats-icon bg-success-light">
                <i class="fas fa-users text-success"></i>
              </div>
              <div class="stats-info">
                <h3 class="stats-value">{{ total_students|default:0 }}</h3>
                <p class="stats-label">Total Students</p>
              </div>
            </div>

            <div class="stats-card">
              <div class="stats-icon bg-info-light">
                <i class="fas fa-chalkboard text-info"></i>
              </div>
              <div class="stats-info">
                <h3 class="stats-value">{{ total_classes|default:0 }}</h3>
                <p class="stats-label">Total Classes</p>
              </div>
            </div>

            <div class="stats-card">
              <div class="stats-icon bg-warning-light">
                <i class="fas fa-user-clock text-warning"></i>
              </div>
              <div class="stats-info">
                <h3 class="stats-value">{{ staff.current_status|title|default:"Active" }}</h3>
                <p class="stats-label">Current Status</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation Tabs -->
  <div class="container-fluid px-4">
    <div class="row">
      <div class="col-md-12">
        <div class="modern-tabs">
          <ul class="nav nav-pills" id="staffProfileTabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" type="button" role="tab" aria-controls="personal" aria-selected="true">
                <i class="fas fa-user me-2"></i>Personal Info
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="professional-tab" data-bs-toggle="tab" data-bs-target="#professional" type="button" role="tab" aria-controls="professional" aria-selected="false">
                <i class="fas fa-briefcase me-2"></i>Professional
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="credentials-tab" data-bs-toggle="tab" data-bs-target="#credentials" type="button" role="tab" aria-controls="credentials" aria-selected="false">
                <i class="fas fa-key me-2"></i>Login Credentials
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab" aria-controls="activity" aria-selected="false">
                <i class="fas fa-chart-line me-2"></i>Activity
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content-header %}

{% block content %}
<div class="row">
  <!-- Left Column - Quick Info -->
  <div class="col-md-4 mb-4">
    <div class="card shadow-sm rounded-3 h-100">
      <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>Quick Information</h5>
      </div>
      <div class="card-body p-4">
        <div class="staff-status text-center mb-4">
          {% if staff.current_status == 'active' %}
            <div class="status-indicator active mx-auto mb-2"></div>
            <h6 class="text-success fw-bold">Active Staff</h6>
          {% else %}
            <div class="status-indicator inactive mx-auto mb-2"></div>
            <h6 class="text-secondary fw-bold">Inactive Staff</h6>
          {% endif %}
          <p class="text-muted small">Last Updated: {{ staff.date_of_registration|default:"N/A" }}</p>
        </div>

        <div class="quick-contact mb-4">
          <h6 class="fw-bold mb-3 border-bottom pb-2 text-primary"><i class="fas fa-address-card me-2"></i>Contact Details</h6>
          <div class="d-flex align-items-center mb-3">
            <div class="contact-icon me-3 bg-primary bg-opacity-10 rounded-circle p-2">
              <i class="fas fa-phone text-primary"></i>
            </div>
            <div>
              <small class="text-muted d-block">Phone Number</small>
              <span class="fw-medium">{{ staff.mobile_number|default:"Not provided" }}</span>
            </div>
          </div>
          <div class="d-flex align-items-center mb-3">
            <div class="contact-icon me-3 bg-danger bg-opacity-10 rounded-circle p-2">
              <i class="fas fa-map-marker-alt text-danger"></i>
            </div>
            <div>
              <small class="text-muted d-block">Address</small>
              <span class="fw-medium">{{ staff.address|default:"Not provided" }}</span>
            </div>
          </div>
          <div class="d-flex align-items-center">
            <div class="contact-icon me-3 bg-success bg-opacity-10 rounded-circle p-2">
              <i class="fas fa-calendar-alt text-success"></i>
            </div>
            <div>
              <small class="text-muted d-block">Date of Birth</small>
              <span class="fw-medium">{{ staff.date_of_birth|default:"N/A" }}</span>
            </div>
          </div>
        </div>

        <div class="staff-specialization mb-4">
          <h6 class="fw-bold mb-3 border-bottom pb-2 text-primary"><i class="fas fa-graduation-cap me-2"></i>Specialization</h6>
          <div class="p-3 bg-light rounded-3 border">
            <p class="mb-0 fw-medium">{{ staff.Subject_specification|default:"Not specified" }}</p>
          </div>
        </div>

        <div class="staff-id-info mb-4">
          <h6 class="fw-bold mb-3 border-bottom pb-2 text-primary"><i class="fas fa-id-badge me-2"></i>ID Information</h6>
          <div class="d-flex align-items-center mb-3">
            <div class="contact-icon me-3 bg-info bg-opacity-10 rounded-circle p-2">
              <i class="fas fa-id-card text-info"></i>
            </div>
            <div>
              <small class="text-muted d-block">Registration Number</small>
              <span class="fw-medium">{{ staff.registration_number|default:"N/A" }}</span>
            </div>
          </div>
          <div class="d-flex align-items-center">
            <div class="contact-icon me-3 bg-warning bg-opacity-10 rounded-circle p-2">
              <i class="fas fa-fingerprint text-warning"></i>
            </div>
            <div>
              <small class="text-muted d-block">Aadhar Number</small>
              <span class="fw-medium">{{ staff.aadhar|default:"Not provided" }}</span>
            </div>
          </div>
        </div>

        <div class="d-grid gap-2">
          {% if staff.mobile_number %}
          <a href="tel:{{ staff.mobile_number }}" class="btn btn-primary">
            <i class="fas fa-phone-alt me-2"></i> Call
          </a>
          {% endif %}
          <a href="{% url 'teacher_dashboard' %}" class="btn btn-success">
            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Right Column - Tab Content -->
  <div class="col-md-8 mb-4">
    <div class="card shadow-sm rounded-3 h-100">
      <div class="card-body p-0">
        <div class="tab-content" id="staffProfileTabsContent">
          <!-- Personal Information Tab -->
          <div class="tab-pane fade show active" id="personal" role="tabpanel" aria-labelledby="personal-tab">
            <div class="p-4">
              <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="fw-bold mb-0"><i class="fas fa-user-circle me-2 text-primary"></i>Personal Information</h5>
              </div>

              <div class="row g-4 mb-4">
                <div class="col-md-6">
                  <div class="info-card p-3 border rounded-3 h-100 bg-light bg-opacity-50">
                    <div class="d-flex align-items-center mb-2">
                      <div class="icon-circle bg-primary text-white me-2">
                        <i class="fas fa-user"></i>
                      </div>
                      <h6 class="fw-bold mb-0">Basic Details</h6>
                    </div>
                    <div class="info-list mt-3">
                      <div class="info-item d-flex mb-2">
                        <div class="info-label text-muted" style="width: 120px;">Full Name:</div>
                        <div class="info-value fw-medium flex-grow-1">{{ staff.fullname|default:"N/A" }}</div>
                      </div>
                      <div class="info-item d-flex mb-2">
                        <div class="info-label text-muted" style="width: 120px;">Gender:</div>
                        <div class="info-value fw-medium flex-grow-1">{{ staff.get_gender_display|default:"N/A" }}</div>
                      </div>
                      <div class="info-item d-flex mb-2">
                        <div class="info-label text-muted" style="width: 120px;">Date of Birth:</div>
                        <div class="info-value fw-medium flex-grow-1">{{ staff.date_of_birth|default:"N/A" }}</div>
                      </div>
                      <div class="info-item d-flex">
                        <div class="info-label text-muted" style="width: 120px;">Status:</div>
                        <div class="info-value fw-medium flex-grow-1">
                          {% if staff.current_status == 'active' %}
                            <span class="badge bg-success">Active</span>
                          {% else %}
                            <span class="badge bg-secondary">Inactive</span>
                          {% endif %}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="col-md-6">
                  <div class="info-card p-3 border rounded-3 h-100 bg-light bg-opacity-50">
                    <div class="d-flex align-items-center mb-2">
                      <div class="icon-circle bg-success text-white me-2">
                        <i class="fas fa-id-card"></i>
                      </div>
                      <h6 class="fw-bold mb-0">Contact & ID</h6>
                    </div>
                    <div class="info-list mt-3">
                      <div class="info-item d-flex mb-2">
                        <div class="info-label text-muted" style="width: 120px;">Mobile:</div>
                        <div class="info-value fw-medium flex-grow-1">{{ staff.mobile_number|default:"Not provided" }}</div>
                      </div>
                      <div class="info-item d-flex mb-2">
                        <div class="info-label text-muted" style="width: 120px;">Aadhar:</div>
                        <div class="info-value fw-medium flex-grow-1">{{ staff.aadhar|default:"Not provided" }}</div>
                      </div>
                      <div class="info-item d-flex mb-2">
                        <div class="info-label text-muted" style="width: 120px;">Reg. Number:</div>
                        <div class="info-value fw-medium flex-grow-1">{{ staff.registration_number|default:"N/A" }}</div>
                      </div>
                      <div class="info-item d-flex">
                        <div class="info-label text-muted" style="width: 120px;">Reg. Date:</div>
                        <div class="info-value fw-medium flex-grow-1">{{ staff.date_of_registration|default:"N/A" }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="address-section border p-3 rounded-3 mb-4 bg-light bg-opacity-50">
                <div class="d-flex align-items-center mb-2">
                  <div class="icon-circle bg-danger text-white me-2">
                    <i class="fas fa-map-marker-alt"></i>
                  </div>
                  <h6 class="fw-bold mb-0">Address</h6>
                </div>
                <p class="mb-0 mt-2 ps-4">{{ staff.address|default:"Address not provided" }}</p>
              </div>

              <div class="notes-section border p-3 rounded-3 bg-light bg-opacity-50">
                <div class="d-flex align-items-center mb-2">
                  <div class="icon-circle bg-warning text-white me-2">
                    <i class="fas fa-sticky-note"></i>
                  </div>
                  <h6 class="fw-bold mb-0">Additional Notes</h6>
                </div>
                <p class="mb-0 mt-2 ps-4">{{ staff.others|default:"No additional notes" }}</p>
              </div>
            </div>
          </div>

          <!-- Professional Tab -->
          <div class="tab-pane fade" id="professional" role="tabpanel" aria-labelledby="professional-tab">
            <div class="p-4">
              <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="fw-bold mb-0"><i class="fas fa-briefcase me-2 text-primary"></i>Professional Information</h5>
              </div>

              <div class="specialization-card mb-4 border rounded-3 overflow-hidden">
                <div class="card-header bg-primary text-white py-3">
                  <h6 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Subject Specialization</h6>
                </div>
                <div class="card-body p-3 bg-light">
                  <div class="specialization-content p-3 bg-white rounded-3 border">
                    <p class="mb-0 fw-medium">{{ staff.Subject_specification|default:"Not specified" }}</p>
                  </div>
                </div>
              </div>

              <div class="classes-card mb-4 border rounded-3 overflow-hidden">
                <div class="card-header bg-success text-white py-3">
                  <h6 class="mb-0"><i class="fas fa-users me-2"></i>Student Statistics</h6>
                </div>
                <div class="card-body p-3 bg-light">
                  <div class="row text-center">
                    <div class="col-md-4">
                      <div class="stat-item p-3 bg-white rounded-3 border">
                        <h4 class="text-primary mb-1">{{ total_students|default:0 }}</h4>
                        <small class="text-muted">Total Students</small>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="stat-item p-3 bg-white rounded-3 border">
                        <h4 class="text-success mb-1">{{ total_classes|default:0 }}</h4>
                        <small class="text-muted">Total Classes</small>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="stat-item p-3 bg-white rounded-3 border">
                        <h4 class="text-info mb-1">0</h4>
                        <small class="text-muted">Assignments</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="recent-students-card border rounded-3 overflow-hidden">
                <div class="card-header bg-info text-white py-3">
                  <h6 class="mb-0"><i class="fas fa-user-graduate me-2"></i>Recent Students</h6>
                </div>
                <div class="card-body p-3 bg-light">
                  {% if recent_students %}
                    <div class="table-responsive bg-white rounded-3 border">
                      <table class="table table-hover mb-0">
                        <thead class="table-light">
                          <tr>
                            <th width="5%" class="text-center">#</th>
                            <th width="40%">Student Name</th>
                            <th width="25%">Class</th>
                            <th width="30%">Admission Date</th>
                          </tr>
                        </thead>
                        <tbody>
                          {% for student in recent_students %}
                          <tr>
                            <td class="text-center">{{ forloop.counter }}</td>
                            <td>{{ student.fullname }}</td>
                            <td>{{ student.current_class.name }} - {{ student.section }}</td>
                            <td>{{ student.date_of_admission|date:"d M Y" }}</td>
                          </tr>
                          {% endfor %}
                        </tbody>
                      </table>
                    </div>
                  {% else %}
                    <div class="text-center py-4">
                      <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                      <p class="text-muted">No recent students found</p>
                    </div>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>

          <!-- Login Credentials Tab -->
          <div class="tab-pane fade" id="credentials" role="tabpanel" aria-labelledby="credentials-tab">
            <div class="p-4">
              <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="fw-bold mb-0"><i class="fas fa-key me-2 text-primary"></i>Login Credentials</h5>
              </div>

              <div class="credentials-card border rounded-3 overflow-hidden">
                <div class="card-header bg-warning text-dark py-3">
                  <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Authentication Details</h6>
                </div>
                <div class="card-body p-4 bg-light">
                  <div class="row g-4">
                    <div class="col-md-6">
                      <div class="credential-item p-3 bg-white rounded-3 border">
                        <label class="form-label fw-bold text-muted mb-2">Login ID</label>
                        <div class="input-group">
                          <span class="input-group-text bg-light"><i class="fas fa-user"></i></span>
                          <input type="text" class="form-control" value="{{ staff.staff_login_id|default:'N/A' }}" readonly>
                          <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ staff.staff_login_id }}')">
                            <i class="fas fa-copy"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="credential-item p-3 bg-white rounded-3 border">
                        <label class="form-label fw-bold text-muted mb-2">Password</label>
                        <div class="input-group">
                          <span class="input-group-text bg-light"><i class="fas fa-lock"></i></span>
                          <input type="password" class="form-control" value="{{ staff.staff_password|default:'N/A' }}" readonly id="passwordField">
                          <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                            <i class="fas fa-eye" id="toggleIcon"></i>
                          </button>
                          <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ staff.staff_password }}')">
                            <i class="fas fa-copy"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="alert alert-info border-0 mt-4">
                    <div class="d-flex align-items-center">
                      <i class="fas fa-shield-alt me-3 fa-2x"></i>
                      <div>
                        <h6 class="mb-1">Security Guidelines</h6>
                        <small>Keep your login credentials secure and do not share them with others. Contact administrator if you need to change your password.</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Activity Tab -->
          <div class="tab-pane fade" id="activity" role="tabpanel" aria-labelledby="activity-tab">
            <div class="p-4">
              <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="fw-bold mb-0"><i class="fas fa-chart-line me-2 text-primary"></i>Activity & Statistics</h5>
              </div>

              <div class="activity-stats mb-4">
                <div class="row g-4">
                  <div class="col-md-3">
                    <div class="stat-card p-3 border rounded-3 text-center bg-primary bg-opacity-10">
                      <i class="fas fa-users fa-2x text-primary mb-2"></i>
                      <h4 class="mb-1">{{ total_students|default:0 }}</h4>
                      <small class="text-muted">Total Students</small>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="stat-card p-3 border rounded-3 text-center bg-success bg-opacity-10">
                      <i class="fas fa-chalkboard fa-2x text-success mb-2"></i>
                      <h4 class="mb-1">{{ total_classes|default:0 }}</h4>
                      <small class="text-muted">Total Classes</small>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="stat-card p-3 border rounded-3 text-center bg-info bg-opacity-10">
                      <i class="fas fa-calendar-check fa-2x text-info mb-2"></i>
                      <h4 class="mb-1">0</h4>
                      <small class="text-muted">Attendance</small>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="stat-card p-3 border rounded-3 text-center bg-warning bg-opacity-10">
                      <i class="fas fa-file-alt fa-2x text-warning mb-2"></i>
                      <h4 class="mb-1">0</h4>
                      <small class="text-muted">Reports</small>
                    </div>
                  </div>
                </div>
              </div>

              <div class="recent-activity-card border rounded-3 overflow-hidden">
                <div class="card-header bg-secondary text-white py-3">
                  <h6 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Activity</h6>
                </div>
                <div class="card-body p-3 bg-light">
                  <div class="activity-timeline">
                    <div class="timeline-item d-flex align-items-center p-3 bg-white rounded-3 border mb-3">
                      <div class="timeline-icon bg-primary text-white rounded-circle me-3 p-2">
                        <i class="fas fa-sign-in-alt"></i>
                      </div>
                      <div>
                        <h6 class="mb-1">Profile Accessed</h6>
                        <small class="text-muted">Today at {{ "now"|date:"H:i" }}</small>
                      </div>
                    </div>
                    <div class="timeline-item d-flex align-items-center p-3 bg-white rounded-3 border mb-3">
                      <div class="timeline-icon bg-success text-white rounded-circle me-3 p-2">
                        <i class="fas fa-user-plus"></i>
                      </div>
                      <div>
                        <h6 class="mb-1">Account Created</h6>
                        <small class="text-muted">{{ staff.date_of_registration|date:"d M Y" }}</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
<!-- Custom CSS for Modern Industry-Ready Staff Profile -->
<style>
  /* Modern Profile Banner */
  .staff-profile-container {
    font-family: 'Roboto', 'Segoe UI', sans-serif;
  }

  .profile-banner {
    background: linear-gradient(135deg, #1a237e, #283593, #3949ab);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('{% static "dist/img/pattern-bg.png" %}') repeat;
    opacity: 0.1;
    z-index: 1;
  }

  .profile-banner .container-fluid {
    position: relative;
    z-index: 2;
  }

  /* Profile Image Styling */
  .profile-image-container {
    position: relative;
  }

  .profile-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid rgba(255, 255, 255, 0.3);
    object-fit: cover;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }

  .status-indicator-modern {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid white;
    font-size: 14px;
    font-weight: bold;
  }

  .status-indicator-modern.active {
    background: #28a745;
    color: white;
  }

  .status-indicator-modern.inactive {
    background: #dc3545;
    color: white;
  }

  /* Staff Info Styling */
  .staff-name {
    font-size: 2.2rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .designation-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    margin-right: 10px;
  }

  .id-badge {
    background: rgba(255, 255, 255, 0.1);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
  }

  .staff-specialization {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-top: 8px;
  }

  /* Stats Cards */
  .stats-card-wrapper {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .stats-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    min-width: 200px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  .stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.5rem;
  }

  .bg-primary-light { background: rgba(0, 123, 255, 0.1); }
  .bg-success-light { background: rgba(40, 167, 69, 0.1); }
  .bg-info-light { background: rgba(23, 162, 184, 0.1); }
  .bg-warning-light { background: rgba(255, 193, 7, 0.1); }

  .stats-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    color: #2c3e50;
  }

  .stats-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
    font-weight: 500;
  }

  /* Modern Tabs */
  .modern-tabs .nav-pills .nav-link {
    background: transparent;
    border: 2px solid #e9ecef;
    color: #6c757d;
    font-weight: 600;
    padding: 12px 20px;
    margin-right: 10px;
    border-radius: 25px;
    transition: all 0.3s ease;
  }

  .modern-tabs .nav-pills .nav-link:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
  }

  .modern-tabs .nav-pills .nav-link.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.3);
  }

  /* Info Cards */
  .icon-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
  }

  .contact-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .status-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: inline-block;
  }

  .status-indicator.active {
    background: #28a745;
  }

  .status-indicator.inactive {
    background: #6c757d;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .stats-card-wrapper {
      flex-direction: column;
      align-items: center;
    }

    .stats-card {
      width: 100%;
      max-width: 300px;
    }

    .staff-name {
      font-size: 1.8rem;
    }

    .profile-image {
      width: 100px;
      height: 100px;
    }
  }
</style>

<script>
function togglePassword() {
    const passwordField = document.getElementById('passwordField');
    const toggleIcon = document.getElementById('toggleIcon');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

function copyToClipboard(text) {
    if (text && text !== 'N/A') {
        navigator.clipboard.writeText(text).then(function() {
            // Show success message
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
            toast.style.zIndex = '9999';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-check me-2"></i>Copied to clipboard!
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            // Remove toast after it's hidden
            toast.addEventListener('hidden.bs.toast', function() {
                document.body.removeChild(toast);
            });
        });
    }
}
</script>
{% endblock content %}
