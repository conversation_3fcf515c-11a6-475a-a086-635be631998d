{% extends 'TeacherDashboard/base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Dashboard
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-user-cog"></i> Profile
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-user-cog{% endblock title-icon %}
{% block title %}My Profile{% endblock title %}
{% block subtitle %}View and manage your profile information{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <!-- Profile Header Card -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-sm border-0">
        <div class="card-body bg-gradient-primary text-white rounded">
          <div class="row align-items-center">
            <div class="col-auto">
              <div class="avatar-lg">
                <div class="avatar-title bg-white text-primary rounded-circle">
                  <i class="fas fa-user-tie fa-2x"></i>
                </div>
              </div>
            </div>
            <div class="col">
              <h4 class="mb-1">{{ staff.fullname|default:"Teacher Name" }}</h4>
              <p class="mb-0 opacity-75">
                <i class="fas fa-id-badge me-2"></i>{{ staff.registration_number|default:"N/A" }}
                <span class="ms-3">
                  <i class="fas fa-calendar me-2"></i>Joined {{ staff.date_of_registration|date:"M Y"|default:"N/A" }}
                </span>
              </p>
            </div>
            <div class="col-auto">
              <span class="badge {% if staff.current_status == 'active' %}bg-success{% else %}bg-warning{% endif %} fs-6">
                <i class="fas fa-circle me-1"></i>{{ staff.current_status|title|default:"Active" }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Personal Information -->
    <div class="col-lg-8">
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white border-bottom">
          <h5 class="mb-0 text-primary"><i class="fas fa-user me-2"></i>Personal Information</h5>
        </div>
        <div class="card-body">
          {% if staff %}
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label fw-bold text-muted">Full Name</label>
                  <p class="form-control-plaintext border-bottom pb-2">{{ staff.fullname }}</p>
                </div>
                <div class="mb-3">
                  <label class="form-label fw-bold text-muted">Registration Number</label>
                  <p class="form-control-plaintext border-bottom pb-2">{{ staff.registration_number|default:"N/A" }}</p>
                </div>
                <div class="mb-3">
                  <label class="form-label fw-bold text-muted">Gender</label>
                  <p class="form-control-plaintext border-bottom pb-2">
                    <i class="fas {% if staff.gender == 'male' %}fa-mars text-primary{% else %}fa-venus text-pink{% endif %} me-2"></i>
                    {{ staff.gender|title }}
                  </p>
                </div>
                <div class="mb-3">
                  <label class="form-label fw-bold text-muted">Date of Birth</label>
                  <p class="form-control-plaintext border-bottom pb-2">
                    <i class="fas fa-birthday-cake text-warning me-2"></i>
                    {{ staff.date_of_birth|date:"d M Y" }}
                  </p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label fw-bold text-muted">Mobile Number</label>
                  <p class="form-control-plaintext border-bottom pb-2">
                    <i class="fas fa-phone text-success me-2"></i>
                    {{ staff.mobile_number|default:"N/A" }}
                  </p>
                </div>
                <div class="mb-3">
                  <label class="form-label fw-bold text-muted">Date of Registration</label>
                  <p class="form-control-plaintext border-bottom pb-2">
                    <i class="fas fa-calendar-plus text-info me-2"></i>
                    {{ staff.date_of_registration|date:"d M Y" }}
                  </p>
                </div>
                <div class="mb-3">
                  <label class="form-label fw-bold text-muted">Subject Specification</label>
                  <p class="form-control-plaintext border-bottom pb-2">
                    <i class="fas fa-book text-primary me-2"></i>
                    {{ staff.Subject_specification|default:"N/A" }}
                  </p>
                </div>
                {% if staff.aadhar %}
                <div class="mb-3">
                  <label class="form-label fw-bold text-muted">Aadhar Number</label>
                  <p class="form-control-plaintext border-bottom pb-2">
                    <i class="fas fa-id-card text-warning me-2"></i>
                    {{ staff.aadhar }}
                  </p>
                </div>
                {% endif %}
              </div>
            </div>

            {% if staff.address %}
            <div class="mb-3">
              <label class="form-label fw-bold text-muted">Address</label>
              <p class="form-control-plaintext border-bottom pb-2">
                <i class="fas fa-map-marker-alt text-danger me-2"></i>
                {{ staff.address }}
              </p>
            </div>
            {% endif %}

            {% if staff.others %}
            <div class="mb-3">
              <label class="form-label fw-bold text-muted">Additional Information</label>
              <p class="form-control-plaintext border-bottom pb-2">
                <i class="fas fa-info-circle text-info me-2"></i>
                {{ staff.others }}
              </p>
            </div>
            {% endif %}
          {% else %}
            <div class="text-center py-5">
              <i class="fas fa-user-times fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">Profile Not Found</h5>
              <p class="text-muted">Your staff profile could not be found. Please contact the administrator.</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
      <!-- Login Credentials -->
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white border-bottom">
          <h5 class="mb-0 text-primary"><i class="fas fa-key me-2"></i>Login Credentials</h5>
        </div>
        <div class="card-body">
          {% if staff %}
            <div class="mb-3">
              <label class="form-label fw-bold text-muted">Login ID</label>
              <div class="input-group">
                <span class="input-group-text bg-light"><i class="fas fa-user"></i></span>
                <input type="text" class="form-control" value="{{ staff.staff_login_id|default:'N/A' }}" readonly>
                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ staff.staff_login_id }}')">
                  <i class="fas fa-copy"></i>
                </button>
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label fw-bold text-muted">Password</label>
              <div class="input-group">
                <span class="input-group-text bg-light"><i class="fas fa-lock"></i></span>
                <input type="password" class="form-control" value="{{ staff.staff_password|default:'N/A' }}" readonly id="passwordField">
                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                  <i class="fas fa-eye" id="toggleIcon"></i>
                </button>
                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ staff.staff_password }}')">
                  <i class="fas fa-copy"></i>
                </button>
              </div>
            </div>
            <div class="alert alert-info border-0">
              <i class="fas fa-shield-alt me-2"></i>
              <small><strong>Security Note:</strong> Keep your login credentials secure and do not share them with others.</small>
            </div>
          {% endif %}
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white border-bottom">
          <h5 class="mb-0 text-primary"><i class="fas fa-chart-bar me-2"></i>Quick Stats</h5>
        </div>
        <div class="card-body">
          <div class="row text-center">
            <div class="col-6">
              <div class="border-end">
                <h4 class="text-primary mb-1">{{ total_students|default:0 }}</h4>
                <small class="text-muted">Total Students</small>
              </div>
            </div>
            <div class="col-6">
              <h4 class="text-success mb-1">{{ total_classes|default:0 }}</h4>
              <small class="text-muted">Total Classes</small>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Students -->
      {% if recent_students %}
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <h5 class="mb-0 text-primary"><i class="fas fa-users me-2"></i>Recent Students</h5>
        </div>
        <div class="card-body p-0">
          <div class="list-group list-group-flush">
            {% for student in recent_students %}
            <div class="list-group-item border-0 py-2">
              <div class="d-flex align-items-center">
                <div class="avatar-sm me-3">
                  <div class="avatar-title bg-light text-primary rounded-circle">
                    <i class="fas fa-user-graduate"></i>
                  </div>
                </div>
                <div class="flex-grow-1">
                  <h6 class="mb-0">{{ student.fullname }}</h6>
                  <small class="text-muted">{{ student.current_class.name }} - {{ student.section }}</small>
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        </div>
      </div>
      {% endif %}
    </div>
  </div>
</div>

<script>
function togglePassword() {
    const passwordField = document.getElementById('passwordField');
    const toggleIcon = document.getElementById('toggleIcon');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check me-2"></i>Copied to clipboard!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // Remove toast after it's hidden
        toast.addEventListener('hidden.bs.toast', function() {
            document.body.removeChild(toast);
        });
    });
}
</script>

<style>
.avatar-lg {
    width: 4rem;
    height: 4rem;
}

.avatar-sm {
    width: 2rem;
    height: 2rem;
}

.avatar-title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 1rem;
    font-weight: 600;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.text-pink {
    color: #e91e63 !important;
}

.border-bottom {
    border-bottom: 1px solid #e9ecef !important;
}
</style>
{% endblock content %}
