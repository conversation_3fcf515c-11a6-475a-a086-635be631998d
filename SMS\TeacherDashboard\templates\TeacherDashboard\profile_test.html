{% extends 'TeacherDashboard/base.html' %}
{% load static %}
{% load humanize %}

{% block title-icon %}fas fa-chalkboard-teacher{% endblock title-icon %}
{% block title %}{{ staff.fullname|default:"My Profile" }}{% endblock title %}
{% block subtitle %}Teaching Staff Profile{% endblock subtitle %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Dashboard
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-user-cog"></i> My Profile
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3>Teacher Profile - Test</h3>
        </div>
        <div class="card-body">
          {% if staff %}
            <h4>Staff Information Found!</h4>
            <table class="table">
              <tr>
                <th>Full Name:</th>
                <td>{{ staff.fullname }}</td>
              </tr>
              <tr>
                <th>Registration Number:</th>
                <td>{{ staff.registration_number }}</td>
              </tr>
              <tr>
                <th>Mobile Number:</th>
                <td>{{ staff.mobile_number }}</td>
              </tr>
              <tr>
                <th>Address:</th>
                <td>{{ staff.address }}</td>
              </tr>
              <tr>
                <th>Subject Specification:</th>
                <td>{{ staff.Subject_specification }}</td>
              </tr>
              <tr>
                <th>Status:</th>
                <td>{{ staff.current_status }}</td>
              </tr>
              <tr>
                <th>Login ID:</th>
                <td>{{ staff.staff_login_id }}</td>
              </tr>
              <tr>
                <th>Password:</th>
                <td>{{ staff.staff_password }}</td>
              </tr>
              <tr>
                <th>Date of Birth:</th>
                <td>{{ staff.date_of_birth }}</td>
              </tr>
              <tr>
                <th>Date of Registration:</th>
                <td>{{ staff.date_of_registration }}</td>
              </tr>
              <tr>
                <th>Aadhar:</th>
                <td>{{ staff.aadhar|default:"Not provided" }}</td>
              </tr>
            </table>
            
            <h4>Statistics</h4>
            <ul>
              <li>Total Students: {{ total_students }}</li>
              <li>Total Classes: {{ total_classes }}</li>
              <li>Recent Students Count: {{ recent_students|length }}</li>
            </ul>
            
            {% if recent_students %}
            <h4>Recent Students</h4>
            <ul>
              {% for student in recent_students %}
              <li>{{ student.fullname }} - {{ student.current_class.name }} {{ student.section }}</li>
              {% endfor %}
            </ul>
            {% endif %}
          {% else %}
            <div class="alert alert-warning">
              <h4>No Staff Data Found</h4>
              <p>Staff profile not found for user: {{ request.user.username }}</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
